import type { CorsOptions } from "cors";

/**
 * Get allowed origins based on environment
 */
export const getAllowedOrigins = (): string[] => {
    switch (process.env.NODE_ENV) {
        // Allow all origins in development
        case "development": {
            return ["*"];
        }
        case "staging": {
            return ["https://test.battleacademy.io", "https://testadmin.battleacademy.io"];
        }
        case "production": {
            return ["https://app.battleacademy.io", "https://admin.battleacademy.io"];
        }
        default: {
            return ["*"];
        }
    }
};

/**
 * CORS configuration for Express
 */
export const corsConfig: CorsOptions = {
    origin: getAllowedOrigins(),
    optionsSuccessStatus: 204,
    credentials: true,
    methods: ["GET", "POST", "PUT", "DELETE", "OPTIONS"],
    allowedHeaders: ["Content-Type", "Authorization", "x-csrf-token"],
    exposedHeaders: ["x-version"],
};
